services:
  # 🌐 Backend (Go REST API)
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: go-api
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=************************************/appdb?sslmode=disable
      - REDIS_URL=redis:6379
    depends_on:
      - db
      - redis
    networks:
      - appnet

  # 🖥️ Frontend (Vue.js)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: vue-frontend
    ports:
      - "3000:80"
    depends_on:
      - api
    networks:
      - appnet

  # 🛢️ PostgreSQL databáze
  db:
    image: postgres:15
    container_name: postgres-db
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: appdb
    volumes:
      - pgdata:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - appnet

  # ⚡ Redis pro cache a task queue
  redis:
    image: redis:7
    container_name: redis
    ports:
      - "6379:6379"
    networks:
      - appnet

  # 🧠 AI microservice (LLM, RAG, doporučování)
  ai:
    build:
      context: ./ai
      dockerfile: Dockerfile
    container_name: ai-service
    ports:
      - "5000:5000"
    depends_on:
      - db
    networks:
      - appnet

  # 🔌 MCP Server pro Augment Code Agent
  mcp-server:
    build:
      context: ./mcp-server
      dockerfile: Dockerfile
    container_name: linda-mcp-server
    ports:
      - "8083:8083"
    networks:
      - appnet

  # 📊 Monitoring: Prometheus + Grafana + Alertmanager
  prometheus:
    image: prom/prometheus
    container_name: prometheus
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
    ports:
      - "9090:9090"
    networks:
      - appnet

  grafana:
    image: grafana/grafana
    container_name: grafana
    ports:
      - "3001:3000"
    depends_on:
      - prometheus
    networks:
      - appnet

  alertmanager:
    image: prom/alertmanager
    container_name: alertmanager
    volumes:
      - ./monitoring/alertmanager.yml:/etc/alertmanager/alertmanager.yml
    ports:
      - "9093:9093"
    networks:
      - appnet

networks:
  appnet:
    driver: bridge

volumes:
  pgdata:
