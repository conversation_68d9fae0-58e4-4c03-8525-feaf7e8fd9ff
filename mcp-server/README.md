# Linda Context MCP Server

Model Context Protocol (MCP) server poskytu<PERSON><PERSON><PERSON><PERSON> kontext pro Linda Dating App projekt pro Augment Code Agent.

## Co je MCP Server?

MCP (Model Context Protocol) server umožňuje AI agentům přístup k externím zdrojům dat a nástrojům. Tento server poskytuje Augment Code Agentovi detailní informace o Linda projektu.

## Funkce

### Resources (Zdroje)
- **Project Overview** - <PERSON><PERSON><PERSON><PERSON> přehled projektu Linda
- **Architecture** - Technická architektura a komponenty
- **API Endpoints** - Dostupné backend API endpointy
- **Frontend Components** - Vue.js komponenty a jejich struktura

### Tools (Nástroje)
- **get_project_status** - Získání aktuálního stavu služeb
- **analyze_code_structure** - Analýza struktury kódu konkrétní komponenty

## Instalace a spuštění

### <PERSON><PERSON>lní vývoj
```bash
cd mcp-server
npm install
npm start
```

### Docker
```bash
docker build -t linda-mcp-server .
docker run -p 8083:8083 linda-mcp-server
```

### Docker Compose
```bash
docker-compose up mcp-server
```

## Konfigurace v VS Code

MCP server je automaticky nakonfigurován v `.vscode/settings.json`:

```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "Linda Context Server",
        "command": "node",
        "args": ["./mcp-server/index.js"]
      }
    ]
  }
}
```

## Použití s Augment Code Agent

Po konfiguraci můžete v Augment Code Agentovi používat příkazy jako:
- "Jaká je architektura Linda projektu?"
- "Ukaž mi API endpointy"
- "Analyzuj frontend komponenty"
- "Jaký je aktuální stav projektu?"

## Vývoj

Pro přidání nových resources nebo tools editujte `index.js` a přidejte nové handlery do příslušných sekcí.

## Závislosti

- `@modelcontextprotocol/sdk` - MCP SDK pro Node.js
- `node` 18+ - Runtime prostředí
